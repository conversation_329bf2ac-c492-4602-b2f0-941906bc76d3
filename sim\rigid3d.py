"""
3D Rigid Body Physics Solver for NeoPhysics.

High-fidelity rigid body dynamics with advanced collision detection, contact resolution,
and numerical stability. Implements the comprehensive physics engine architecture
with proper inertia tensors, multiple geometry representations, and constraint-based solving.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum
import warnings
from abc import ABC, abstractmethod

class BodyType(Enum):
    """Types of rigid bodies."""
    DYNAMIC = "dynamic"  # Affected by forces
    STATIC = "static"    # Fixed in place
    KINEMATIC = "kinematic"  # Moves but not affected by forces

class GeometryType(Enum):
    """Types of collision geometry."""
    SPHERE = "sphere"
    BOX = "box"
    CAPSULE = "capsule"
    CONVEX_HULL = "convex_hull"
    TRIANGLE_MESH = "triangle_mesh"

@dataclass
class GeometryData:
    """Base class for collision geometry data."""
    geometry_type: GeometryType

class SphereGeometry(GeometryData):
    """Sphere collision geometry."""
    def __init__(self, radius: float):
        super().__init__(GeometryType.SPHERE)
        self.radius = radius

class BoxGeometry(GeometryData):
    """Box collision geometry."""
    def __init__(self, half_extents: np.ndarray):
        super().__init__(GeometryType.BOX)
        self.half_extents = np.array(half_extents)  # [x, y, z] half-sizes

class ConvexHullGeometry(GeometryData):
    """Convex hull collision geometry."""
    def __init__(self, vertices: np.ndarray):
        super().__init__(GeometryType.CONVEX_HULL)
        self.vertices = np.array(vertices)  # Nx3 array of vertices

class TriangleMeshGeometry(GeometryData):
    """Triangle mesh collision geometry."""
    def __init__(self, vertices: np.ndarray, indices: np.ndarray):
        super().__init__(GeometryType.TRIANGLE_MESH)
        self.vertices = np.array(vertices)  # Nx3 array of vertices
        self.indices = np.array(indices)    # Mx3 array of triangle indices

@dataclass
class InertiaTensor:
    """3x3 inertia tensor in body coordinates."""
    tensor: np.ndarray = field(default_factory=lambda: np.eye(3))

    def __post_init__(self):
        """Ensure tensor is 3x3."""
        if self.tensor.shape != (3, 3):
            raise ValueError("Inertia tensor must be 3x3")

    @classmethod
    def sphere(cls, mass: float, radius: float) -> 'InertiaTensor':
        """Create inertia tensor for solid sphere."""
        inertia = (2.0/5.0) * mass * radius**2
        return cls(np.diag([inertia, inertia, inertia]))

    @classmethod
    def box(cls, mass: float, dimensions: np.ndarray) -> 'InertiaTensor':
        """Create inertia tensor for solid box."""
        x, y, z = dimensions
        ixx = (mass / 12.0) * (y*y + z*z)
        iyy = (mass / 12.0) * (x*x + z*z)
        izz = (mass / 12.0) * (x*x + y*y)
        return cls(np.diag([ixx, iyy, izz]))

    def inverse(self) -> np.ndarray:
        """Get inverse inertia tensor."""
        return np.linalg.inv(self.tensor)

@dataclass
class RigidBody:
    """A rigid body with comprehensive physics properties and geometry representations."""
    id: str
    name: str
    body_type: BodyType = BodyType.DYNAMIC

    # Transform
    position: np.ndarray = field(default_factory=lambda: np.zeros(3))
    rotation: np.ndarray = field(default_factory=lambda: np.zeros(4))  # quaternion [w,x,y,z]

    # Physics properties
    mass: float = 1.0
    center_of_mass: np.ndarray = field(default_factory=lambda: np.zeros(3))  # In body coordinates
    inertia_tensor: InertiaTensor = field(default_factory=lambda: InertiaTensor.sphere(1.0, 0.5))
    restitution: float = 0.6  # Bounciness
    friction: float = 0.3

    # Geometry representations
    collision_geometry: GeometryData = field(default_factory=lambda: SphereGeometry(0.5))
    visual_mesh: Optional[TriangleMeshGeometry] = None
    convex_decomposition: List[ConvexHullGeometry] = field(default_factory=list)

    # State
    velocity: np.ndarray = field(default_factory=lambda: np.zeros(3))
    angular_velocity: np.ndarray = field(default_factory=lambda: np.zeros(3))
    force: np.ndarray = field(default_factory=lambda: np.zeros(3))
    torque: np.ndarray = field(default_factory=lambda: np.zeros(3))

    # Cached properties for performance
    _world_inertia_tensor: Optional[np.ndarray] = field(default=None, init=False)
    _world_inertia_inverse: Optional[np.ndarray] = field(default=None, init=False)

    # Legacy compatibility
    radius: float = field(default=0.5, init=False)  # For backward compatibility

    def __post_init__(self):
        """Initialize quaternion to identity and set up legacy compatibility."""
        # Ensure all arrays are float64
        self.position = np.array(self.position, dtype=np.float64)
        self.velocity = np.array(self.velocity, dtype=np.float64)
        self.angular_velocity = np.array(self.angular_velocity, dtype=np.float64)
        self.force = np.array(self.force, dtype=np.float64)
        self.torque = np.array(self.torque, dtype=np.float64)
        self.center_of_mass = np.array(self.center_of_mass, dtype=np.float64)

        if np.allclose(self.rotation, 0):
            self.rotation = np.array([1.0, 0.0, 0.0, 0.0], dtype=np.float64)  # Identity quaternion
        else:
            self.rotation = np.array(self.rotation, dtype=np.float64)

        # Set legacy radius for backward compatibility
        if isinstance(self.collision_geometry, SphereGeometry):
            self.radius = self.collision_geometry.radius
        elif isinstance(self.collision_geometry, BoxGeometry):
            self.radius = np.linalg.norm(self.collision_geometry.half_extents)
        else:
            self.radius = 0.5  # Default fallback

    def get_world_inertia_tensor(self) -> np.ndarray:
        """Get inertia tensor in world coordinates."""
        if self._world_inertia_tensor is None:
            R = self.get_rotation_matrix()
            I_body = self.inertia_tensor.tensor
            self._world_inertia_tensor = R @ I_body @ R.T
        return self._world_inertia_tensor

    def get_world_inertia_inverse(self) -> np.ndarray:
        """Get inverse inertia tensor in world coordinates."""
        if self._world_inertia_inverse is None:
            R = self.get_rotation_matrix()
            I_body_inv = self.inertia_tensor.inverse()
            self._world_inertia_inverse = R @ I_body_inv @ R.T
        return self._world_inertia_inverse

    def get_rotation_matrix(self) -> np.ndarray:
        """Convert quaternion to 3x3 rotation matrix."""
        w, x, y, z = self.rotation
        return np.array([
            [1-2*(y*y+z*z), 2*(x*y-w*z), 2*(x*z+w*y)],
            [2*(x*y+w*z), 1-2*(x*x+z*z), 2*(y*z-w*x)],
            [2*(x*z-w*y), 2*(y*z+w*x), 1-2*(x*x+y*y)]
        ])

    def invalidate_cached_properties(self):
        """Invalidate cached world-space properties when rotation changes."""
        self._world_inertia_tensor = None
        self._world_inertia_inverse = None

@dataclass
class ContactPoint:
    """A single contact point between two bodies."""
    body1_id: str
    body2_id: str
    position: np.ndarray  # World space contact position
    normal: np.ndarray    # Contact normal (from body1 to body2)
    penetration: float    # Penetration depth

    # Contact properties
    restitution: float = 0.0
    friction: float = 0.0

    # Cached impulse for warm starting
    normal_impulse: float = 0.0
    tangent_impulse: np.ndarray = field(default_factory=lambda: np.zeros(2))

@dataclass
class ContactManifold:
    """A contact manifold between two bodies."""
    body1_id: str
    body2_id: str
    contacts: List[ContactPoint] = field(default_factory=list)
    normal: np.ndarray = field(default_factory=lambda: np.zeros(3))

    # Persistence data
    frame_count: int = 0
    last_update_frame: int = 0

class ConstraintType(Enum):
    """Types of physics constraints."""
    FIXED_JOINT = "fixed_joint"
    HINGE_JOINT = "hinge_joint"
    BALL_JOINT = "ball_joint"
    DISTANCE_CONSTRAINT = "distance_constraint"

@dataclass
class Constraint(ABC):
    """Base class for physics constraints."""
    id: str
    body1_id: str
    body2_id: str
    constraint_type: ConstraintType
    enabled: bool = True

    @abstractmethod
    def solve(self, body1: RigidBody, body2: RigidBody, dt: float):
        """Solve the constraint."""
        pass

@dataclass
class FixedJoint(Constraint):
    """Fixed joint constraint - bodies maintain relative position and orientation."""
    relative_position: np.ndarray = field(default_factory=lambda: np.zeros(3))
    relative_rotation: np.ndarray = field(default_factory=lambda: np.array([1.0, 0.0, 0.0, 0.0]))

    def __post_init__(self):
        super().__init__(
            id=self.id,
            body1_id=self.body1_id,
            body2_id=self.body2_id,
            constraint_type=ConstraintType.FIXED_JOINT
        )

    def solve(self, body1: RigidBody, body2: RigidBody, dt: float):
        """Solve fixed joint constraint."""
        if not self.enabled:
            return

        # Calculate desired position for body2 based on body1
        R1 = body1.get_rotation_matrix()
        desired_pos = body1.position + R1 @ self.relative_position

        # Position correction
        position_error = body2.position - desired_pos
        correction_factor = 0.1  # Soft constraint

        if body1.body_type == BodyType.DYNAMIC:
            body1.position += position_error * correction_factor * 0.5
        if body2.body_type == BodyType.DYNAMIC:
            body2.position -= position_error * correction_factor * 0.5

@dataclass
class HingeJoint(Constraint):
    """Hinge joint constraint - allows rotation around one axis."""
    anchor_point: np.ndarray = field(default_factory=lambda: np.zeros(3))
    hinge_axis: np.ndarray = field(default_factory=lambda: np.array([0.0, 0.0, 1.0]))

    def __post_init__(self):
        super().__init__(
            id=self.id,
            body1_id=self.body1_id,
            body2_id=self.body2_id,
            constraint_type=ConstraintType.HINGE_JOINT
        )
        self.hinge_axis = self.hinge_axis / np.linalg.norm(self.hinge_axis)

    def solve(self, body1: RigidBody, body2: RigidBody, dt: float):
        """Solve hinge joint constraint."""
        if not self.enabled:
            return

        # Keep bodies connected at anchor point
        r1 = self.anchor_point - body1.position
        r2 = self.anchor_point - body2.position

        # Position constraint
        position_error = (body1.position + r1) - (body2.position + r2)
        correction_factor = 0.1

        if body1.body_type == BodyType.DYNAMIC:
            body1.position -= position_error * correction_factor * 0.5
        if body2.body_type == BodyType.DYNAMIC:
            body2.position += position_error * correction_factor * 0.5

@dataclass
class DistanceConstraint(Constraint):
    """Distance constraint - maintains fixed distance between two points."""
    distance: float = 1.0
    anchor1: np.ndarray = field(default_factory=lambda: np.zeros(3))  # Local to body1
    anchor2: np.ndarray = field(default_factory=lambda: np.zeros(3))  # Local to body2

    def __post_init__(self):
        super().__init__(
            id=self.id,
            body1_id=self.body1_id,
            body2_id=self.body2_id,
            constraint_type=ConstraintType.DISTANCE_CONSTRAINT
        )

    def solve(self, body1: RigidBody, body2: RigidBody, dt: float):
        """Solve distance constraint."""
        if not self.enabled:
            return

        # World positions of anchor points
        R1 = body1.get_rotation_matrix()
        R2 = body2.get_rotation_matrix()

        world_anchor1 = body1.position + R1 @ self.anchor1
        world_anchor2 = body2.position + R2 @ self.anchor2

        # Current distance and error
        current_vector = world_anchor2 - world_anchor1
        current_distance = np.linalg.norm(current_vector)

        if current_distance < 1e-6:
            return

        distance_error = current_distance - self.distance
        correction_direction = current_vector / current_distance

        # Apply position correction
        correction_factor = 0.1
        correction = correction_direction * distance_error * correction_factor

        if body1.body_type == BodyType.DYNAMIC:
            body1.position += correction * 0.5
        if body2.body_type == BodyType.DYNAMIC:
            body2.position -= correction * 0.5

class RigidBodySolver:
    """High-fidelity 3D rigid body physics solver with advanced collision detection."""

    def __init__(self, gravity: float = 9.81):
        self.gravity = gravity
        self.bodies: Dict[str, RigidBody] = {}
        self.time = 0.0
        self.dt = 1.0 / 60.0  # 60 FPS
        self.frame_count = 0

        # Ground plane
        self.ground_y = 0.0
        self.ground_enabled = True

        # Contact management
        self.contact_manifolds: Dict[Tuple[str, str], ContactManifold] = {}
        self.contact_threshold = 0.01  # Contact generation threshold

        # Constraint management
        self.constraints: Dict[str, Constraint] = {}

        # Solver parameters
        self.solver_iterations = 10
        self.position_correction_factor = 0.2  # Baumgarte stabilization
        self.velocity_threshold = 0.01  # Sleep threshold

        # Performance settings
        self.enable_ccd = True
        self.enable_warm_starting = True
        self.enable_friction = True
    
    def add_body(self, body: RigidBody) -> str:
        """Add a rigid body to the simulation."""
        self.bodies[body.id] = body
        return body.id
    
    def remove_body(self, body_id: str) -> bool:
        """Remove a body from simulation."""
        if body_id in self.bodies:
            del self.bodies[body_id]
            return True
        return False
    
    def get_body(self, body_id: str) -> Optional[RigidBody]:
        """Get a body by ID."""
        return self.bodies.get(body_id)

    def add_constraint(self, constraint: Constraint) -> str:
        """Add a constraint to the simulation."""
        self.constraints[constraint.id] = constraint
        return constraint.id

    def remove_constraint(self, constraint_id: str) -> bool:
        """Remove a constraint from simulation."""
        if constraint_id in self.constraints:
            del self.constraints[constraint_id]
            return True
        return False

    def get_constraint(self, constraint_id: str) -> Optional[Constraint]:
        """Get a constraint by ID."""
        return self.constraints.get(constraint_id)

    def create_fixed_joint(self, constraint_id: str, body1_id: str, body2_id: str) -> FixedJoint:
        """Create a fixed joint between two bodies."""
        body1 = self.get_body(body1_id)
        body2 = self.get_body(body2_id)

        if not body1 or not body2:
            raise ValueError(f"Bodies not found: {body1_id}, {body2_id}")

        # Calculate relative position and rotation
        relative_pos = body2.position - body1.position
        # For simplicity, assume no relative rotation initially
        relative_rot = np.array([1.0, 0.0, 0.0, 0.0])

        constraint = FixedJoint(
            id=constraint_id,
            body1_id=body1_id,
            body2_id=body2_id,
            relative_position=relative_pos,
            relative_rotation=relative_rot
        )

        self.add_constraint(constraint)
        return constraint

    def create_hinge_joint(self, constraint_id: str, body1_id: str, body2_id: str,
                          anchor_point: np.ndarray, hinge_axis: np.ndarray) -> HingeJoint:
        """Create a hinge joint between two bodies."""
        body1 = self.get_body(body1_id)
        body2 = self.get_body(body2_id)

        if not body1 or not body2:
            raise ValueError(f"Bodies not found: {body1_id}, {body2_id}")

        constraint = HingeJoint(
            id=constraint_id,
            body1_id=body1_id,
            body2_id=body2_id,
            anchor_point=np.array(anchor_point),
            hinge_axis=np.array(hinge_axis)
        )

        self.add_constraint(constraint)
        return constraint

    def create_distance_constraint(self, constraint_id: str, body1_id: str, body2_id: str,
                                 distance: float, anchor1: np.ndarray = None,
                                 anchor2: np.ndarray = None) -> DistanceConstraint:
        """Create a distance constraint between two bodies."""
        body1 = self.get_body(body1_id)
        body2 = self.get_body(body2_id)

        if not body1 or not body2:
            raise ValueError(f"Bodies not found: {body1_id}, {body2_id}")

        if anchor1 is None:
            anchor1 = np.zeros(3)
        if anchor2 is None:
            anchor2 = np.zeros(3)

        constraint = DistanceConstraint(
            id=constraint_id,
            body1_id=body1_id,
            body2_id=body2_id,
            distance=distance,
            anchor1=np.array(anchor1),
            anchor2=np.array(anchor2)
        )

        self.add_constraint(constraint)
        return constraint

    def create_sphere_body(self, body_id: str, name: str, position: np.ndarray,
                          radius: float, mass: float = 1.0, **kwargs) -> RigidBody:
        """Create a sphere rigid body with proper geometry and inertia."""
        geometry = SphereGeometry(radius)
        inertia = InertiaTensor.sphere(mass, radius)

        body = RigidBody(
            id=body_id,
            name=name,
            position=np.array(position),
            mass=mass,
            collision_geometry=geometry,
            inertia_tensor=inertia,
            **kwargs
        )

        self.add_body(body)
        return body

    def create_box_body(self, body_id: str, name: str, position: np.ndarray,
                       dimensions: np.ndarray, mass: float = 1.0, **kwargs) -> RigidBody:
        """Create a box rigid body with proper geometry and inertia."""
        half_extents = np.array(dimensions) / 2.0
        geometry = BoxGeometry(half_extents)
        inertia = InertiaTensor.box(mass, dimensions)

        body = RigidBody(
            id=body_id,
            name=name,
            position=np.array(position),
            mass=mass,
            collision_geometry=geometry,
            inertia_tensor=inertia,
            **kwargs
        )

        self.add_body(body)
        return body
    
    def set_gravity(self, gravity: float):
        """Set gravity acceleration."""
        self.gravity = gravity
    
    def set_ground_level(self, y: float):
        """Set ground plane Y level."""
        self.ground_y = y
    
    def step(self, dt: Optional[float] = None):
        """Advance simulation by one time step using advanced physics pipeline."""
        if dt is None:
            dt = self.dt

        self.frame_count += 1

        # 1. Apply external forces (gravity, user forces)
        self._apply_forces()

        # 2. Broad phase collision detection
        potential_pairs = self._broad_phase_collision_detection()

        # 3. Narrow phase collision detection and contact generation
        self._narrow_phase_collision_detection(potential_pairs)

        # 4. Contact manifold management and persistence
        self._update_contact_manifolds()

        # 5. Constraint solving (contact resolution)
        self._solve_constraints(dt)

        # 5.5. Solve user-defined constraints
        self._solve_user_constraints(dt)

        # 6. Integrate motion (symplectic semi-implicit)
        self._integrate_motion(dt)

        # 7. Update cached properties
        self._update_cached_properties()

        # Update time
        self.time += dt
    
    def _apply_forces(self):
        """Apply external forces to all bodies."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue

            # Clear previous forces (external forces are applied each frame)
            body.force.fill(0)
            body.torque.fill(0)

            # Apply gravity at center of mass
            gravity_force = np.array([0, -body.mass * self.gravity, 0])
            body.force += gravity_force

            # Note: Torque from gravity is zero if applied at center of mass
    
    def _broad_phase_collision_detection(self) -> List[Tuple[str, str]]:
        """Broad phase collision detection using simple AABB overlap."""
        potential_pairs = []
        body_list = list(self.bodies.values())

        for i in range(len(body_list)):
            for j in range(i + 1, len(body_list)):
                body1, body2 = body_list[i], body_list[j]

                # Skip if both bodies are static or kinematic
                if (body1.body_type != BodyType.DYNAMIC and
                    body2.body_type != BodyType.DYNAMIC):
                    continue

                # Simple sphere-based AABB check for now
                # TODO: Implement proper AABB hierarchy
                distance = np.linalg.norm(body1.position - body2.position)
                combined_radius = self._get_bounding_radius(body1) + self._get_bounding_radius(body2)

                if distance < combined_radius * 1.1:  # Small margin for broad phase
                    potential_pairs.append((body1.id, body2.id))

        return potential_pairs

    def _get_bounding_radius(self, body: RigidBody) -> float:
        """Get conservative bounding radius for a body."""
        if isinstance(body.collision_geometry, SphereGeometry):
            return body.collision_geometry.radius
        elif isinstance(body.collision_geometry, BoxGeometry):
            return np.linalg.norm(body.collision_geometry.half_extents)
        else:
            return body.radius  # Fallback to legacy radius

    def _narrow_phase_collision_detection(self, potential_pairs: List[Tuple[str, str]]):
        """Narrow phase collision detection and contact point generation."""
        for body1_id, body2_id in potential_pairs:
            body1 = self.bodies[body1_id]
            body2 = self.bodies[body2_id]

            # For now, implement sphere-sphere collision detection
            # TODO: Implement GJK+EPA for general convex shapes
            if (isinstance(body1.collision_geometry, SphereGeometry) and
                isinstance(body2.collision_geometry, SphereGeometry)):
                self._detect_sphere_sphere_collision(body1, body2)

    def _detect_sphere_sphere_collision(self, body1: RigidBody, body2: RigidBody):
        """Detect collision between two spheres."""
        sphere1 = body1.collision_geometry
        sphere2 = body2.collision_geometry

        distance_vec = body2.position - body1.position
        distance = np.linalg.norm(distance_vec)
        min_distance = sphere1.radius + sphere2.radius

        if distance < min_distance and distance > 1e-8:
            # Collision detected
            normal = distance_vec / distance
            penetration = min_distance - distance
            contact_pos = body1.position + normal * sphere1.radius

            # Create contact point
            contact = ContactPoint(
                body1_id=body1.id,
                body2_id=body2.id,
                position=contact_pos,
                normal=normal,
                penetration=penetration,
                restitution=min(body1.restitution, body2.restitution),
                friction=np.sqrt(body1.friction * body2.friction)  # Geometric mean
            )

            # Add to manifold
            manifold_key = (body1.id, body2.id) if body1.id < body2.id else (body2.id, body1.id)
            if manifold_key not in self.contact_manifolds:
                self.contact_manifolds[manifold_key] = ContactManifold(
                    body1_id=body1.id, body2_id=body2.id, normal=normal
                )

            manifold = self.contact_manifolds[manifold_key]
            manifold.contacts = [contact]  # Single contact for sphere-sphere
            manifold.normal = normal
            manifold.last_update_frame = self.frame_count

    def _update_contact_manifolds(self):
        """Update and clean up contact manifolds."""
        # Remove old manifolds that weren't updated this frame
        keys_to_remove = []
        for key, manifold in self.contact_manifolds.items():
            if manifold.last_update_frame < self.frame_count:
                keys_to_remove.append(key)
            else:
                manifold.frame_count += 1

        for key in keys_to_remove:
            del self.contact_manifolds[key]

    def _solve_constraints(self, dt: float):
        """Solve contact constraints using iterative method."""
        # Ground collision handling (legacy compatibility)
        self._handle_ground_collisions()

        # Iterative constraint solver
        for iteration in range(self.solver_iterations):
            for manifold in self.contact_manifolds.values():
                for contact in manifold.contacts:
                    self._solve_contact_constraint(contact, dt)

    def _solve_user_constraints(self, dt: float):
        """Solve user-defined constraints (joints, etc.)."""
        for constraint in self.constraints.values():
            if not constraint.enabled:
                continue

            body1 = self.get_body(constraint.body1_id)
            body2 = self.get_body(constraint.body2_id)

            if body1 and body2:
                constraint.solve(body1, body2, dt)

    def _solve_contact_constraint(self, contact: ContactPoint, dt: float):
        """Solve a single contact constraint."""
        body1 = self.bodies[contact.body1_id]
        body2 = self.bodies[contact.body2_id]

        # Calculate relative velocity at contact point
        r1 = contact.position - body1.position
        r2 = contact.position - body2.position

        v1 = body1.velocity + np.cross(body1.angular_velocity, r1)
        v2 = body2.velocity + np.cross(body2.angular_velocity, r2)
        relative_velocity = v2 - v1

        # Normal impulse calculation
        normal_velocity = np.dot(relative_velocity, contact.normal)

        if normal_velocity < 0:  # Objects approaching
            # Calculate impulse magnitude
            restitution_velocity = -contact.restitution * normal_velocity
            desired_delta_velocity = restitution_velocity - normal_velocity

            # Calculate effective mass
            effective_mass = self._calculate_effective_mass(body1, body2, contact.normal, r1, r2)

            if effective_mass > 0:
                impulse_magnitude = desired_delta_velocity / effective_mass
                impulse = impulse_magnitude * contact.normal

                # Apply impulse
                if body1.body_type == BodyType.DYNAMIC:
                    body1.velocity -= impulse / body1.mass
                    body1.angular_velocity -= body1.get_world_inertia_inverse() @ np.cross(r1, impulse)

                if body2.body_type == BodyType.DYNAMIC:
                    body2.velocity += impulse / body2.mass
                    body2.angular_velocity += body2.get_world_inertia_inverse() @ np.cross(r2, impulse)

    def _calculate_effective_mass(self, body1: RigidBody, body2: RigidBody,
                                normal: np.ndarray, r1: np.ndarray, r2: np.ndarray) -> float:
        """Calculate effective mass for contact constraint."""
        effective_mass = 0.0

        if body1.body_type == BodyType.DYNAMIC:
            effective_mass += 1.0 / body1.mass
            cross1 = np.cross(r1, normal)
            effective_mass += np.dot(cross1, body1.get_world_inertia_inverse() @ cross1)

        if body2.body_type == BodyType.DYNAMIC:
            effective_mass += 1.0 / body2.mass
            cross2 = np.cross(r2, normal)
            effective_mass += np.dot(cross2, body2.get_world_inertia_inverse() @ cross2)

        return effective_mass

    def _handle_ground_collisions(self):
        """Handle ground collisions (legacy compatibility)."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue

            if self.ground_enabled:
                ground_contact = body.position[1] - self._get_bounding_radius(body)
                if ground_contact <= self.ground_y:
                    # Resolve penetration
                    body.position[1] = self.ground_y + self._get_bounding_radius(body)

                    # Apply collision response
                    if body.velocity[1] < 0:  # Moving downward
                        body.velocity[1] = -body.velocity[1] * body.restitution

                        # Apply friction
                        horizontal_vel = np.array([body.velocity[0], 0, body.velocity[2]])
                        if np.linalg.norm(horizontal_vel) > 1e-6:
                            friction_force = -horizontal_vel * body.friction
                            body.velocity[0] += friction_force[0] * self.dt
                            body.velocity[2] += friction_force[2] * self.dt

    def _update_cached_properties(self):
        """Update cached properties for all bodies."""
        for body in self.bodies.values():
            # Cached properties are updated lazily when accessed
            pass

    def _integrate_motion(self, dt: float):
        """Integrate motion using symplectic semi-implicit method."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue

            # Linear motion (semi-implicit Euler)
            acceleration = body.force / body.mass
            body.velocity += acceleration * dt
            body.position += body.velocity * dt

            # Angular motion with proper inertia tensor
            world_inertia_inv = body.get_world_inertia_inverse()
            angular_acceleration = world_inertia_inv @ body.torque
            body.angular_velocity += angular_acceleration * dt

            # Proper quaternion integration
            self._integrate_rotation(body, dt)

            # Invalidate cached properties after rotation update
            body.invalidate_cached_properties()

    def _integrate_rotation(self, body: RigidBody, dt: float):
        """Integrate rotation using proper quaternion methods."""
        omega = body.angular_velocity
        omega_magnitude = np.linalg.norm(omega)

        if omega_magnitude > 1e-8:
            # Exponential map integration
            axis = omega / omega_magnitude
            angle = omega_magnitude * dt
            dq = self._axis_angle_to_quaternion(axis, angle)
            body.rotation = self._multiply_quaternions(body.rotation, dq)
            body.rotation = body.rotation / np.linalg.norm(body.rotation)
    
    def _handle_collisions(self):
        """Handle collisions with ground and other objects."""
        for body in self.bodies.values():
            if body.body_type != BodyType.DYNAMIC:
                continue
            
            # Ground collision
            if self.ground_enabled:
                ground_contact = body.position[1] - body.radius
                if ground_contact <= self.ground_y:
                    # Resolve penetration
                    body.position[1] = self.ground_y + body.radius
                    
                    # Apply collision response
                    if body.velocity[1] < 0:  # Moving downward
                        body.velocity[1] = -body.velocity[1] * body.restitution
                        
                        # Apply friction
                        horizontal_vel = np.array([body.velocity[0], 0, body.velocity[2]])
                        if np.linalg.norm(horizontal_vel) > 1e-6:
                            friction_force = -horizontal_vel * body.friction
                            body.velocity[0] += friction_force[0] * self.dt
                            body.velocity[2] += friction_force[2] * self.dt
        
        # Object-object collisions (simplified sphere-sphere)
        body_list = list(self.bodies.values())
        for i in range(len(body_list)):
            for j in range(i + 1, len(body_list)):
                body1, body2 = body_list[i], body_list[j]
                
                if (body1.body_type != BodyType.DYNAMIC and 
                    body2.body_type != BodyType.DYNAMIC):
                    continue
                
                # Check collision
                distance = np.linalg.norm(body1.position - body2.position)
                min_distance = body1.radius + body2.radius
                
                if distance < min_distance and distance > 1e-6:
                    # Collision normal
                    normal = (body2.position - body1.position) / distance
                    
                    # Resolve penetration
                    penetration = min_distance - distance
                    if body1.body_type == BodyType.DYNAMIC:
                        body1.position -= normal * penetration * 0.5
                    if body2.body_type == BodyType.DYNAMIC:
                        body2.position += normal * penetration * 0.5
                    
                    # Apply collision response
                    relative_velocity = body2.velocity - body1.velocity
                    velocity_along_normal = np.dot(relative_velocity, normal)
                    
                    if velocity_along_normal > 0:
                        continue  # Objects separating
                    
                    # Calculate restitution
                    restitution = min(body1.restitution, body2.restitution)
                    
                    # Calculate impulse
                    impulse_magnitude = -(1 + restitution) * velocity_along_normal
                    if body1.body_type == BodyType.DYNAMIC and body2.body_type == BodyType.DYNAMIC:
                        impulse_magnitude /= (1/body1.mass + 1/body2.mass)
                    elif body1.body_type == BodyType.DYNAMIC:
                        impulse_magnitude /= (1/body1.mass)
                    elif body2.body_type == BodyType.DYNAMIC:
                        impulse_magnitude /= (1/body2.mass)
                    
                    impulse = impulse_magnitude * normal
                    
                    # Apply impulse
                    if body1.body_type == BodyType.DYNAMIC:
                        body1.velocity -= impulse / body1.mass
                    if body2.body_type == BodyType.DYNAMIC:
                        body2.velocity += impulse / body2.mass
    
    def _axis_angle_to_quaternion(self, axis: np.ndarray, angle: float) -> np.ndarray:
        """Convert axis-angle to quaternion."""
        half_angle = angle * 0.5
        sin_half = np.sin(half_angle)
        cos_half = np.cos(half_angle)
        return np.array([cos_half, axis[0]*sin_half, axis[1]*sin_half, axis[2]*sin_half])
    
    def _multiply_quaternions(self, q1: np.ndarray, q2: np.ndarray) -> np.ndarray:
        """Multiply two quaternions."""
        w1, x1, y1, z1 = q1
        w2, x2, y2, z2 = q2
        return np.array([
            w1*w2 - x1*x2 - y1*y2 - z1*z2,
            w1*x2 + x1*w2 + y1*z2 - z1*y2,
            w1*y2 - x1*z2 + y1*w2 + z1*x2,
            w1*z2 + x1*y2 - y1*x2 + z1*w2
        ])
    
    def get_stats(self) -> Dict[str, float]:
        """Get comprehensive simulation statistics."""
        total_energy = 0.0
        total_momentum = np.zeros(3)
        total_angular_momentum = np.zeros(3)

        for body in self.bodies.values():
            if body.body_type == BodyType.DYNAMIC:
                # Linear kinetic energy
                ke_linear = 0.5 * body.mass * np.dot(body.velocity, body.velocity)

                # Rotational kinetic energy
                I_world = body.get_world_inertia_tensor()
                ke_rotational = 0.5 * np.dot(body.angular_velocity, I_world @ body.angular_velocity)

                # Potential energy
                pe = body.mass * self.gravity * body.position[1]

                total_energy += ke_linear + ke_rotational + pe

                # Linear momentum
                total_momentum += body.mass * body.velocity

                # Angular momentum
                total_angular_momentum += I_world @ body.angular_velocity

        return {
            'time': self.time,
            'frame': self.frame_count,
            'total_energy': total_energy,
            'momentum_magnitude': np.linalg.norm(total_momentum),
            'angular_momentum_magnitude': np.linalg.norm(total_angular_momentum),
            'num_bodies': len(self.bodies),
            'num_contacts': sum(len(m.contacts) for m in self.contact_manifolds.values()),
            'num_manifolds': len(self.contact_manifolds)
        }
    
    def reset(self):
        """Reset simulation state."""
        self.time = 0.0
        self.frame_count = 0
        self.contact_manifolds.clear()

        for body in self.bodies.values():
            body.velocity.fill(0)
            body.angular_velocity.fill(0)
            body.force.fill(0)
            body.torque.fill(0)
            body.invalidate_cached_properties()