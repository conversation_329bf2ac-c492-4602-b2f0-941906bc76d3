#!/usr/bin/env python3
"""
Test NLP parsing directly.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from nlp.parser import parse_command

def test_parsing():
    """Test NLP parsing."""
    print("Testing NLP parsing...")
    
    commands = [
        "create a physics sphere sphere1 at position 0,2,0 with mass 1kg",
        "create a physics sphere sphere2 at position 1,2,0 with mass 1kg",
        "connect sphere1 and sphere2 with fixed joint",
        "create hinge joint between sphere1 and sphere2"
    ]
    
    for cmd in commands:
        print(f"\nCommand: '{cmd}'")
        actions = parse_command(cmd)
        
        if not actions:
            print(f"  ❌ No actions parsed")
            continue
        
        for action in actions:
            print(f"  Action: {action.type}")
            print(f"  Params: {action.parameters}")

if __name__ == "__main__":
    test_parsing()
