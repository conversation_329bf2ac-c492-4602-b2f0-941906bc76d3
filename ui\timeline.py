"""
Timeline for NeoPhysics.

Provides simulation playback controls, time scrubbing, and replay functionality.
"""

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton, QSlider,
                            QLabel, QSpinBox, QDoubleSpinBox, QCheckBox, QFileDialog,
                            QMessageBox, QProgressBar)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon
from typing import Optional
from core.simulation_recorder import SimulationRecorder

class Timeline(QWidget):
    """Timeline widget for simulation control and replay."""

    play_requested = pyqtSignal()
    pause_requested = pyqtSignal()
    step_requested = pyqtSignal()
    reset_requested = pyqtSignal()
    time_changed = pyqtSignal(float)  # time
    recording_started = pyqtSignal(str)  # description
    recording_stopped = pyqtSignal()
    replay_frame_requested = pyqtSignal(object)  # SimulationFrame

    def __init__(self):
        super().__init__()
        self.is_playing = False
        self.current_time = 0.0
        self.max_time = 10.0
        self.time_step = 0.01
        self.recorder = SimulationRecorder()
        self.is_replaying = False
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the timeline UI."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # Main controls layout
        layout = QHBoxLayout()
        main_layout.addLayout(layout)
        
        # Play controls
        self.play_btn = QPushButton("▶")
        self.play_btn.setMaximumWidth(40)
        self.play_btn.clicked.connect(self.toggle_play)
        layout.addWidget(self.play_btn)
        
        self.step_btn = QPushButton("⏭")
        self.step_btn.setMaximumWidth(40)
        self.step_btn.clicked.connect(self.step_simulation)
        layout.addWidget(self.step_btn)
        
        self.reset_btn = QPushButton("⏹")
        self.reset_btn.setMaximumWidth(40)
        self.reset_btn.clicked.connect(self.reset_simulation)
        layout.addWidget(self.reset_btn)
        
        # Time display
        self.time_label = QLabel("Time:")
        layout.addWidget(self.time_label)
        
        self.time_display = QLabel("0.00s")
        self.time_display.setMinimumWidth(60)
        layout.addWidget(self.time_display)
        
        # Time slider
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setMinimum(0)
        self.time_slider.setMaximum(1000)
        self.time_slider.setValue(0)
        self.time_slider.valueChanged.connect(self.on_slider_changed)
        layout.addWidget(self.time_slider)
        
        # Time step control
        layout.addWidget(QLabel("dt:"))
        
        self.dt_spin = QDoubleSpinBox()
        self.dt_spin.setRange(0.001, 1.0)
        self.dt_spin.setSingleStep(0.001)
        self.dt_spin.setDecimals(3)
        self.dt_spin.setValue(self.time_step)
        self.dt_spin.setSuffix("s")
        self.dt_spin.setMaximumWidth(80)
        self.dt_spin.valueChanged.connect(self.set_time_step)
        layout.addWidget(self.dt_spin)
        
        # Max time control
        layout.addWidget(QLabel("Max:"))
        
        self.max_time_spin = QDoubleSpinBox()
        self.max_time_spin.setRange(1.0, 1000.0)
        self.max_time_spin.setSingleStep(1.0)
        self.max_time_spin.setValue(self.max_time)
        self.max_time_spin.setSuffix("s")
        self.max_time_spin.setMaximumWidth(80)
        self.max_time_spin.valueChanged.connect(self.set_max_time)
        layout.addWidget(self.max_time_spin)

        # Recording and replay controls
        recording_layout = QHBoxLayout()
        main_layout.addLayout(recording_layout)

        # Recording controls
        self.record_checkbox = QCheckBox("Record")
        self.record_checkbox.toggled.connect(self.toggle_recording)
        recording_layout.addWidget(self.record_checkbox)

        self.save_btn = QPushButton("Save Recording")
        self.save_btn.clicked.connect(self.save_recording)
        self.save_btn.setEnabled(False)
        recording_layout.addWidget(self.save_btn)

        self.load_btn = QPushButton("Load Recording")
        self.load_btn.clicked.connect(self.load_recording)
        recording_layout.addWidget(self.load_btn)

        # Replay controls
        self.replay_checkbox = QCheckBox("Replay Mode")
        self.replay_checkbox.toggled.connect(self.toggle_replay_mode)
        recording_layout.addWidget(self.replay_checkbox)

        recording_layout.addStretch()

        # Progress bar for recording/replay
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def toggle_play(self):
        """Toggle play/pause."""
        if self.is_playing:
            self.pause_simulation()
        else:
            self.play_simulation()
    
    def play_simulation(self):
        """Start playing simulation."""
        self.is_playing = True
        self.play_btn.setText("⏸")
        self.play_requested.emit()
    
    def pause_simulation(self):
        """Pause simulation."""
        self.is_playing = False
        self.play_btn.setText("▶")
        self.pause_requested.emit()
    
    def step_simulation(self):
        """Step simulation by one frame."""
        self.step_requested.emit()
    
    def reset_simulation(self):
        """Reset simulation to beginning."""
        self.is_playing = False
        self.play_btn.setText("▶")
        self.current_time = 0.0
        self.update_display()
        self.reset_requested.emit()
    
    def set_time(self, time: float):
        """Set current time."""
        self.current_time = max(0.0, min(time, self.max_time))
        self.update_display()
    
    def set_time_step(self, dt: float):
        """Set time step."""
        self.time_step = dt
    
    def set_max_time(self, max_time: float):
        """Set maximum time."""
        self.max_time = max_time
        if self.current_time > max_time:
            self.current_time = max_time
        self.update_display()
    
    def update_display(self):
        """Update time display and slider."""
        # Update time label
        self.time_display.setText(f"{self.current_time:.2f}s")
        
        # Update slider position
        if self.max_time > 0:
            slider_pos = int(1000 * self.current_time / self.max_time)
            self.time_slider.blockSignals(True)
            self.time_slider.setValue(slider_pos)
            self.time_slider.blockSignals(False)
    
    def advance_time(self):
        """Advance time by one step."""
        self.set_time(self.current_time + self.time_step)
        self.time_changed.emit(self.current_time)

    def toggle_recording(self, enabled: bool):
        """Toggle simulation recording."""
        if enabled:
            description = f"Simulation recorded at {self.current_time:.2f}s"
            self.recorder.start_recording(description)
            self.recording_started.emit(description)
            self.save_btn.setEnabled(False)
        else:
            self.recorder.stop_recording()
            self.recording_stopped.emit()
            self.save_btn.setEnabled(True)

    def save_recording(self):
        """Save recorded simulation data."""
        if not self.recorder.frames:
            QMessageBox.warning(self, "No Data", "No simulation data to save.")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Simulation Recording",
            f"simulation_{len(self.recorder.frames)}_frames.h5",
            "HDF5 Files (*.h5);;JSON Files (*.json)"
        )

        if filename:
            try:
                if filename.endswith('.json'):
                    self.recorder.save_to_json(filename)
                else:
                    self.recorder.save_to_hdf5(filename)
                QMessageBox.information(self, "Success", f"Recording saved to {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save recording: {str(e)}")

    def load_recording(self):
        """Load simulation recording for replay."""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Load Simulation Recording", "",
            "HDF5 Files (*.h5);;JSON Files (*.json)"
        )

        if filename:
            try:
                success = self.recorder.load_from_hdf5(filename)
                if success:
                    # Update timeline to match recording duration
                    duration = self.recorder.get_duration()
                    self.set_max_time(duration)
                    self.set_time(0.0)
                    self.replay_checkbox.setEnabled(True)
                    QMessageBox.information(
                        self, "Success",
                        f"Loaded {self.recorder.get_frame_count()} frames ({duration:.2f}s)"
                    )
                else:
                    QMessageBox.critical(self, "Error", "Failed to load recording")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load recording: {str(e)}")

    def toggle_replay_mode(self, enabled: bool):
        """Toggle replay mode."""
        self.is_replaying = enabled
        if enabled and self.recorder.frames:
            # Disable live simulation controls
            self.play_btn.setEnabled(False)
            self.step_btn.setEnabled(False)
            # Enable replay scrubbing
            self.time_slider.setEnabled(True)
        else:
            # Re-enable live simulation controls
            self.play_btn.setEnabled(True)
            self.step_btn.setEnabled(True)

    def record_frame(self, time: float, rigid_solver, scene=None):
        """Record a frame of simulation data."""
        if self.recorder.is_recording:
            self.recorder.record_frame(time, rigid_solver, scene)

    def on_slider_changed(self, value: int):
        """Handle slider position change."""
        time = (value / 1000.0) * self.max_time
        self.set_time(time)

        if self.is_replaying and self.recorder.frames:
            # Find and emit the frame for this time
            frame = self.recorder.get_frame_at_time(time)
            if frame:
                self.replay_frame_requested.emit(frame)
        else:
            self.time_changed.emit(self.current_time)

    def get_recorder(self) -> SimulationRecorder:
        """Get the simulation recorder."""
        return self.recorder