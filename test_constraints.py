#!/usr/bin/env python3
"""
Test physics constraints system.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_constraints():
    """Test physics constraints."""
    print("Testing physics constraints...")
    
    scene = Scene("Constraints Test")
    executor = ActionExecutor(scene)
    
    # Create some objects first
    print("\n1. Creating objects...")
    commands = [
        "create a physics sphere sphere1 at position 0,2,0 with mass 1kg",
        "create a physics sphere sphere2 at position 1,2,0 with mass 1kg"
    ]
    
    for cmd in commands:
        actions = parse_command(cmd)
        for action in actions:
            result = executor.execute(action)
            if result["success"]:
                print(f"  ✓ {result['message']}")
            else:
                print(f"  ❌ {result['error']}")
    
    # Check what objects were actually created
    print("\n1.5. Checking created objects...")
    if executor.rigid_solver:
        for body in executor.rigid_solver.bodies.values():
            print(f"  Body: {body.name} (ID: {body.id})")

    # Test constraint commands
    print("\n2. Testing constraint commands...")
    constraint_commands = [
        "connect sphere1 and sphere2 with fixed joint",
        "create hinge joint between sphere1 and sphere2"
    ]
    
    for cmd in constraint_commands:
        print(f"\nTesting: '{cmd}'")
        actions = parse_command(cmd)
        
        if not actions:
            print(f"  ❌ No actions parsed")
            continue
        
        for action in actions:
            print(f"  Action: {action.type}")
            print(f"  Params: {action.parameters}")
            
            result = executor.execute(action)
            if result["success"]:
                print(f"  ✓ {result['message']}")
            else:
                print(f"  ❌ {result['error']}")
    
    # Test physics simulation with constraints
    print(f"\n3. Testing physics simulation with constraints...")
    physics_actions = parse_command("run physics for 5 steps")
    for action in physics_actions:
        result = executor.execute(action)
        if result["success"]:
            print(f"  ✓ {result['message']}")
        else:
            print(f"  ❌ {result['error']}")
    
    # Step physics a few times to see constraints in action
    if executor.rigid_solver:
        print(f"\n4. Stepping physics simulation...")
        for i in range(5):
            result = executor.step_physics(1.0/60.0)
            if result["success"]:
                stats = result.get("stats", {})
                print(f"  Step {i+1}: {stats.get('num_constraints', 0)} constraints active")
            else:
                print(f"  ❌ Step {i+1} failed: {result['error']}")
    
    # Print final scene summary
    print(f"\nFinal scene summary:")
    summary = scene.get_summary()
    print(f"  Objects: {summary['num_objects']}")
    print(f"  Fields: {summary['num_fields']}")
    
    if executor.rigid_solver:
        print(f"  Constraints: {len(executor.rigid_solver.constraints)}")

if __name__ == "__main__":
    test_constraints()
    print("\n🎉 Constraints test complete!")
