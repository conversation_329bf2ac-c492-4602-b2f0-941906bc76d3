"""
Simulation Data Recorder for NeoPhysics.

Records and replays physics simulation data for research and analysis.
"""

import json
import h5py
import numpy as np
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class SimulationFrame:
    """A single frame of simulation data."""
    time: float
    frame_number: int
    bodies: Dict[str, Dict[str, Any]]  # body_id -> {position, velocity, rotation, etc.}
    constraints: Dict[str, Dict[str, Any]]  # constraint_id -> constraint data
    stats: Dict[str, float]  # simulation statistics

class SimulationRecorder:
    """Records simulation data for replay and analysis."""
    
    def __init__(self):
        self.frames: List[SimulationFrame] = []
        self.is_recording = False
        self.current_frame = 0
        self.metadata = {
            "version": "1.0",
            "created_at": "",
            "description": "",
            "total_frames": 0,
            "duration": 0.0,
            "fps": 60.0
        }
    
    def start_recording(self, description: str = ""):
        """Start recording simulation data."""
        self.frames.clear()
        self.is_recording = True
        self.current_frame = 0
        self.metadata["description"] = description
        self.metadata["created_at"] = str(np.datetime64('now'))
        print(f"Started recording simulation: {description}")
    
    def stop_recording(self):
        """Stop recording simulation data."""
        self.is_recording = False
        self.metadata["total_frames"] = len(self.frames)
        if self.frames:
            self.metadata["duration"] = self.frames[-1].time
        print(f"Stopped recording. Captured {len(self.frames)} frames")
    
    def record_frame(self, time: float, rigid_solver, scene=None):
        """Record a single frame of simulation data."""
        if not self.is_recording:
            return
        
        # Record rigid body data
        bodies_data = {}
        if rigid_solver:
            for body_id, body in rigid_solver.bodies.items():
                bodies_data[body_id] = {
                    "name": body.name,
                    "position": body.position.tolist(),
                    "velocity": body.velocity.tolist(),
                    "rotation": body.rotation.tolist(),
                    "angular_velocity": body.angular_velocity.tolist(),
                    "mass": body.mass,
                    "body_type": body.body_type.value
                }
        
        # Record constraint data
        constraints_data = {}
        if rigid_solver and hasattr(rigid_solver, 'constraints'):
            for constraint_id, constraint in rigid_solver.constraints.items():
                constraints_data[constraint_id] = {
                    "type": constraint.constraint_type.value,
                    "body1_id": constraint.body1_id,
                    "body2_id": constraint.body2_id,
                    "enabled": constraint.enabled
                }
        
        # Record simulation statistics
        stats = {}
        if rigid_solver:
            stats = rigid_solver.get_stats()
        
        frame = SimulationFrame(
            time=time,
            frame_number=self.current_frame,
            bodies=bodies_data,
            constraints=constraints_data,
            stats=stats
        )
        
        self.frames.append(frame)
        self.current_frame += 1
    
    def save_to_hdf5(self, filepath: str):
        """Save recorded data to HDF5 file."""
        if not self.frames:
            print("No frames to save")
            return
        
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        with h5py.File(filepath, 'w') as f:
            # Save metadata
            meta_group = f.create_group('metadata')
            for key, value in self.metadata.items():
                meta_group.attrs[key] = value
            
            # Save frame data
            frames_group = f.create_group('frames')
            
            # Create datasets for time series data
            num_frames = len(self.frames)
            times = np.array([frame.time for frame in self.frames])
            frames_group.create_dataset('time', data=times)
            
            # Save body data
            if self.frames[0].bodies:
                bodies_group = frames_group.create_group('bodies')
                
                # Get all unique body IDs
                all_body_ids = set()
                for frame in self.frames:
                    all_body_ids.update(frame.bodies.keys())
                
                for body_id in all_body_ids:
                    body_group = bodies_group.create_group(body_id)
                    
                    # Extract time series for this body
                    positions = []
                    velocities = []
                    rotations = []
                    angular_velocities = []
                    
                    for frame in self.frames:
                        if body_id in frame.bodies:
                            body_data = frame.bodies[body_id]
                            positions.append(body_data['position'])
                            velocities.append(body_data['velocity'])
                            rotations.append(body_data['rotation'])
                            angular_velocities.append(body_data['angular_velocity'])
                        else:
                            # Body doesn't exist in this frame, use zeros
                            positions.append([0, 0, 0])
                            velocities.append([0, 0, 0])
                            rotations.append([1, 0, 0, 0])
                            angular_velocities.append([0, 0, 0])
                    
                    body_group.create_dataset('position', data=np.array(positions))
                    body_group.create_dataset('velocity', data=np.array(velocities))
                    body_group.create_dataset('rotation', data=np.array(rotations))
                    body_group.create_dataset('angular_velocity', data=np.array(angular_velocities))
                    
                    # Save static properties
                    if self.frames[0].bodies.get(body_id):
                        first_frame_data = self.frames[0].bodies[body_id]
                        body_group.attrs['name'] = first_frame_data['name']
                        body_group.attrs['mass'] = first_frame_data['mass']
                        body_group.attrs['body_type'] = first_frame_data['body_type']
            
            # Save statistics
            if self.frames[0].stats:
                stats_group = frames_group.create_group('stats')
                
                # Get all unique stat keys
                all_stat_keys = set()
                for frame in self.frames:
                    all_stat_keys.update(frame.stats.keys())
                
                for stat_key in all_stat_keys:
                    stat_values = []
                    for frame in self.frames:
                        stat_values.append(frame.stats.get(stat_key, 0.0))
                    
                    stats_group.create_dataset(stat_key, data=np.array(stat_values))
        
        print(f"Saved simulation data to {filepath}")
    
    def save_to_json(self, filepath: str):
        """Save recorded data to JSON file (for smaller datasets)."""
        if not self.frames:
            print("No frames to save")
            return
        
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        data = {
            "metadata": self.metadata,
            "frames": [asdict(frame) for frame in self.frames]
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Saved simulation data to {filepath}")
    
    def load_from_hdf5(self, filepath: str):
        """Load recorded data from HDF5 file."""
        filepath = Path(filepath)
        if not filepath.exists():
            print(f"File not found: {filepath}")
            return False
        
        self.frames.clear()
        
        with h5py.File(filepath, 'r') as f:
            # Load metadata
            meta_group = f['metadata']
            for key in meta_group.attrs:
                self.metadata[key] = meta_group.attrs[key]
            
            # Load frame data
            frames_group = f['frames']
            times = frames_group['time'][:]
            
            # Load body data
            bodies_data = {}
            if 'bodies' in frames_group:
                bodies_group = frames_group['bodies']
                for body_id in bodies_group.keys():
                    body_group = bodies_group[body_id]
                    bodies_data[body_id] = {
                        'positions': body_group['position'][:],
                        'velocities': body_group['velocity'][:],
                        'rotations': body_group['rotation'][:],
                        'angular_velocities': body_group['angular_velocity'][:],
                        'name': body_group.attrs['name'],
                        'mass': body_group.attrs['mass'],
                        'body_type': body_group.attrs['body_type']
                    }
            
            # Load statistics
            stats_data = {}
            if 'stats' in frames_group:
                stats_group = frames_group['stats']
                for stat_key in stats_group.keys():
                    stats_data[stat_key] = stats_group[stat_key][:]
            
            # Reconstruct frames
            for i, time in enumerate(times):
                frame_bodies = {}
                for body_id, body_data in bodies_data.items():
                    frame_bodies[body_id] = {
                        'name': body_data['name'],
                        'position': body_data['positions'][i].tolist(),
                        'velocity': body_data['velocities'][i].tolist(),
                        'rotation': body_data['rotations'][i].tolist(),
                        'angular_velocity': body_data['angular_velocities'][i].tolist(),
                        'mass': body_data['mass'],
                        'body_type': body_data['body_type']
                    }
                
                frame_stats = {}
                for stat_key, stat_values in stats_data.items():
                    frame_stats[stat_key] = float(stat_values[i])
                
                frame = SimulationFrame(
                    time=time,
                    frame_number=i,
                    bodies=frame_bodies,
                    constraints={},  # TODO: Load constraints
                    stats=frame_stats
                )
                self.frames.append(frame)
        
        print(f"Loaded {len(self.frames)} frames from {filepath}")
        return True
    
    def get_frame_at_time(self, time: float) -> Optional[SimulationFrame]:
        """Get the frame closest to the specified time."""
        if not self.frames:
            return None
        
        # Find closest frame
        closest_frame = min(self.frames, key=lambda f: abs(f.time - time))
        return closest_frame
    
    def get_frame_count(self) -> int:
        """Get total number of recorded frames."""
        return len(self.frames)
    
    def get_duration(self) -> float:
        """Get total duration of recorded simulation."""
        if not self.frames:
            return 0.0
        return self.frames[-1].time
    
    def clear(self):
        """Clear all recorded data."""
        self.frames.clear()
        self.is_recording = False
        self.current_frame = 0
