"""
Action Executor for NeoPhysics.

Executes parsed natural language actions on the scene and physics solvers.
"""

import numpy as np
from typing import Dict, Any, Optional
from core.scene import Scene, SceneObject, ObjectType, Transform
from sim.rigid3d import RigidBodySolver, RigidBody, BodyType
from nlp.parser import Action

class ActionExecutor:
    """Executes actions on the physics simulation."""
    
    def __init__(self, scene: Scene):
        self.scene = scene
        self.rigid_solver: Optional[RigidBodySolver] = None
        self.simulation_running = False
    
    def execute(self, action: Action) -> Dict[str, Any]:
        """Execute an action and return result."""
        action_type = action.type
        params = action.parameters
        
        try:
            if action_type == "add_cube":
                return self._add_cube(params)
            elif action_type == "add_ball":
                return self._add_ball(params)
            elif action_type == "add_physics_box":
                return self._add_physics_box(params)
            elif action_type == "add_physics_sphere":
                return self._add_physics_sphere(params)
            elif action_type == "add_sphere":
                return self._add_sphere(params)
            elif action_type == "add_plane":
                return self._add_plane(params)
            elif action_type == "set_position":
                return self._set_position(params)
            elif action_type == "set_velocity":
                return self._set_velocity(params)
            elif action_type == "set_mass":
                return self._set_mass(params)
            elif action_type == "run_physics":
                return self._run_physics(params)
            elif action_type == "clear_scene":
                return self._clear_scene(params)
            elif action_type == "query_scene":
                return self._query_scene(params)
            elif action_type == "create_temperature_field":
                return self._create_temperature_field(params)
            elif action_type == "spawn_multiple_objects":
                return self._spawn_multiple_objects(params)
            elif action_type == "create_tower":
                return self._create_tower(params)
            elif action_type == "create_domino_run":
                return self._create_domino_run(params)
            elif action_type == "create_newtons_cradle":
                return self._create_newtons_cradle(params)
            elif action_type == "create_fixed_joint":
                return self._create_fixed_joint(params)
            elif action_type == "create_hinge_joint":
                return self._create_hinge_joint(params)
            elif action_type == "create_distance_constraint":
                return self._create_distance_constraint(params)
            else:
                return {"success": False, "error": f"Unknown action: {action_type}"}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _add_cube(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a cube to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        size = params.get("size", 1.0)
        
        obj_id = self.scene.create_cube(
            name=name,
            position=tuple(position),
            size=size,
            material="default_thermal",
            temperature=20.0
        )
        
        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created cube '{obj.name}' at {position}"
        }
    
    def _add_sphere(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a sphere to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        radius = params.get("radius", 0.5)
        
        obj_id = self.scene.create_sphere(
            name=name,
            position=tuple(position),
            radius=radius,
            material="default_thermal",
            temperature=20.0
        )
        
        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created sphere '{obj.name}' at {position}"
        }
    
    def _add_plane(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a plane to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        size = params.get("size", 2.0)
        
        transform = Transform(position=np.array(position), scale=np.array([size, size, 0.1]))
        
        obj = SceneObject(
            name=name or "plane",
            object_type=ObjectType.PLANE,
            transform=transform,
            material=self.scene.materials.get("default_thermal"),
            properties={"temperature": params.get("temperature", 20.0)}
        )
        
        obj_id = self.scene.add_object(obj)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created plane '{obj.name}' at {position}"
        }
    

    
    def _set_position(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Set object position."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        
        obj = self.scene.get_object_by_name(name)
        if not obj:
            return {"success": False, "error": f"Object '{name}' not found"}
        
        obj.transform.position = np.array(position)
        return {
            "success": True,
            "message": f"Moved '{name}' to {position}"
        }
    

    
    def _clear_scene(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Clear the scene."""
        self.scene.clear()
        self.rigid_solver = None
        return {"success": True, "message": "Scene cleared"}
    
    def _query_scene(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Query scene information."""
        summary = self.scene.get_summary()
        return {"success": True, "data": summary, "message": "Scene summary retrieved"}
    
    def _add_ball(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a physics ball to the scene."""
        name = params.get("name", "")
        position = params.get("position", [0, 2, 0])  # Default 2m high
        radius = params.get("radius", 0.1)  # 10cm radius
        mass = params.get("mass", 1.0)
        velocity = params.get("velocity", [0, 0, 0])
        
        # Create scene object
        obj_id = self.scene.create_sphere(
            name=name or "ball",
            position=tuple(position),
            radius=radius,
            material="default_thermal",
            temperature=20.0
        )
        
        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()
        
        # Create rigid body using enhanced system
        rigid_body = self.rigid_solver.create_sphere_body(
            body_id=obj_id,
            name=name or "ball",
            position=np.array(position, dtype=float),
            radius=radius,
            mass=mass,
            body_type=BodyType.DYNAMIC,
            velocity=np.array(velocity, dtype=float)
        )
        
        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()
        
        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created physics ball '{obj.name}' at {position} with mass {mass}kg"
        }
    
    def _run_physics(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Run rigid body physics simulation."""
        if self.rigid_solver is None:
            return {"success": False, "error": "No rigid bodies in scene"}
        
        return {
            "success": True,
            "message": "Started rigid body physics simulation"
        }
    
    def _set_velocity(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Set object velocity."""
        name = params.get("name", "")
        velocity = params.get("velocity", [0, 0, 0])
        
        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}
        
        # Find rigid body by name
        for body in self.rigid_solver.bodies.values():
            if body.name == name:
                body.velocity = np.array(velocity, dtype=float)
                return {
                    "success": True,
                    "message": f"Set velocity of '{name}' to {velocity} m/s"
                }
        
        return {"success": False, "error": f"Physics body '{name}' not found"}
    
    def _set_mass(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Set object mass."""
        name = params.get("name", "")
        mass = params.get("mass", 1.0)
        
        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}
        
        # Find rigid body by name
        for body in self.rigid_solver.bodies.values():
            if body.name == name:
                body.mass = mass
                return {
                    "success": True,
                    "message": f"Set mass of '{name}' to {mass} kg"
                }
        
        return {"success": False, "error": f"Physics body '{name}' not found"}
    

    
    def step_physics(self, dt: float = None) -> Dict[str, Any]:
        """Step the physics simulation."""
        if self.rigid_solver is not None:
            self.rigid_solver.step(dt)
            
            # Update scene object positions
            for body in self.rigid_solver.bodies.values():
                obj = self.scene.get_object(body.id)
                if obj:
                    obj.transform.position = body.position.copy()
            
            return {"success": True, "stats": self.rigid_solver.get_stats()}
        
        return {"success": False, "error": "No active physics simulation"}
    
    def get_rigid_solver(self) -> Optional[RigidBodySolver]:
        """Get the rigid body solver."""
        return self.rigid_solver
    
    def set_gravity(self, gravity: float):
        """Set gravity for rigid body simulation."""
        if self.rigid_solver is not None:
            self.rigid_solver.set_gravity(gravity)

    def _add_physics_box(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a physics box with enhanced rigid body features."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        dimensions = params.get("dimensions", [1.0, 1.0, 1.0])
        mass = params.get("mass", 1.0)
        velocity = params.get("velocity", [0, 0, 0])
        restitution = params.get("restitution", 0.6)
        friction = params.get("friction", 0.3)

        # Create scene object first
        obj_id = self.scene.create_cube(
            name=name or "box",
            position=tuple(position),
            size=max(dimensions),  # Use largest dimension for visual size
            material="default_thermal",
            temperature=20.0
        )

        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()

        # Create rigid body using enhanced system
        rigid_body = self.rigid_solver.create_box_body(
            body_id=obj_id,
            name=name or "box",
            position=np.array(position, dtype=float),
            dimensions=np.array(dimensions, dtype=float),
            mass=mass,
            body_type=BodyType.DYNAMIC,
            velocity=np.array(velocity, dtype=float),
            restitution=restitution,
            friction=friction
        )

        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created physics box '{obj.name}' at {position} with mass {mass}kg"
        }

    def _add_physics_sphere(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add a physics sphere with enhanced rigid body features."""
        name = params.get("name", "")
        position = params.get("position", [0, 0, 0])
        radius = params.get("radius", 0.5)
        mass = params.get("mass", 1.0)
        velocity = params.get("velocity", [0, 0, 0])
        restitution = params.get("restitution", 0.6)
        friction = params.get("friction", 0.3)

        # Create scene object first
        obj_id = self.scene.create_sphere(
            name=name or "physics_sphere",
            position=tuple(position),
            radius=radius,
            material="default_thermal",
            temperature=20.0
        )

        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()

        # Create rigid body using enhanced system
        rigid_body = self.rigid_solver.create_sphere_body(
            body_id=obj_id,
            name=name or "physics_sphere",
            position=np.array(position, dtype=float),
            radius=radius,
            mass=mass,
            body_type=BodyType.DYNAMIC,
            velocity=np.array(velocity, dtype=float),
            restitution=restitution,
            friction=friction
        )

        obj = self.scene.get_object(obj_id)
        return {
            "success": True,
            "object_id": obj_id,
            "message": f"Created physics sphere '{obj.name}' at {position} with mass {mass}kg"
        }

    def _create_temperature_field(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a temperature field."""
        name = params.get("name", "temperature")
        grid_size = params.get("grid_size", [32, 32, 32])

        field_id = self.scene.create_temperature_field(
            name=name,
            grid_size=tuple(grid_size)
        )

        return {
            "success": True,
            "field_id": field_id,
            "message": f"Created temperature field '{name}' with grid size {grid_size}"
        }

    def _spawn_multiple_objects(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Spawn multiple objects with random properties."""
        import random

        object_type = params.get("object_type", "sphere")
        count = params.get("count", 10)
        position_range = params.get("position_range", [[-2, -2, 0], [2, 2, 4]])
        velocity_range = params.get("velocity_range", [[-1, -1, 0], [1, 1, 2]])
        size_range = params.get("size_range", [0.1, 0.3])
        mass_range = params.get("mass_range", [0.5, 2.0])

        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()

        created_objects = []

        for i in range(count):
            # Random position
            pos = [
                random.uniform(position_range[0][0], position_range[1][0]),
                random.uniform(position_range[0][1], position_range[1][1]),
                random.uniform(position_range[0][2], position_range[1][2])
            ]

            # Random velocity
            vel = [
                random.uniform(velocity_range[0][0], velocity_range[1][0]),
                random.uniform(velocity_range[0][1], velocity_range[1][1]),
                random.uniform(velocity_range[0][2], velocity_range[1][2])
            ]

            # Random size and mass
            size = random.uniform(size_range[0], size_range[1])
            mass = random.uniform(mass_range[0], mass_range[1])

            # Create object
            name = f"{object_type}_{i:03d}"

            if object_type == "sphere":
                obj_id = self.scene.create_sphere(
                    name=name,
                    position=tuple(pos),
                    radius=size,
                    material="default_thermal"
                )

                # Create rigid body
                rigid_body = self.rigid_solver.create_sphere_body(
                    body_id=obj_id,
                    name=name,
                    position=np.array(pos, dtype=float),
                    radius=size,
                    mass=mass,
                    velocity=np.array(vel, dtype=float)
                )

            elif object_type == "cube":
                obj_id = self.scene.create_cube(
                    name=name,
                    position=tuple(pos),
                    size=size,
                    material="default_thermal"
                )

                # Create rigid body
                dimensions = np.array([size, size, size])
                rigid_body = self.rigid_solver.create_box_body(
                    body_id=obj_id,
                    name=name,
                    position=np.array(pos, dtype=float),
                    dimensions=dimensions,
                    mass=mass,
                    velocity=np.array(vel, dtype=float)
                )

            created_objects.append(obj_id)

        return {
            "success": True,
            "object_ids": created_objects,
            "message": f"Spawned {count} {object_type}s with random properties"
        }

    def _create_tower(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a tower of stacked objects."""
        object_type = params.get("object_type", "cube")
        height = params.get("height", 5)
        position = params.get("position", [0, 0, 0])

        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()

        created_objects = []
        cube_size = 0.5

        for i in range(height):
            pos = [position[0], position[1] + i * cube_size + cube_size/2, position[2]]
            name = f"tower_cube_{i:02d}"

            obj_id = self.scene.create_cube(
                name=name,
                position=tuple(pos),
                size=cube_size,
                material="default_thermal"
            )

            # Create rigid body
            dimensions = np.array([cube_size, cube_size, cube_size])
            rigid_body = self.rigid_solver.create_box_body(
                body_id=obj_id,
                name=name,
                position=np.array(pos, dtype=float),
                dimensions=dimensions,
                mass=1.0
            )

            created_objects.append(obj_id)

        return {
            "success": True,
            "object_ids": created_objects,
            "message": f"Created tower with {height} cubes"
        }

    def _create_domino_run(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a domino run."""
        count = params.get("count", 10)
        spacing = params.get("spacing", 0.5)
        position = params.get("position", [0, 0, 0])

        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()

        created_objects = []
        domino_size = [0.1, 0.8, 0.4]  # thin, tall dominoes

        for i in range(count):
            pos = [position[0] + i * spacing, position[1] + domino_size[1]/2, position[2]]
            name = f"domino_{i:02d}"

            obj_id = self.scene.create_cube(
                name=name,
                position=tuple(pos),
                size=domino_size[0],  # Use smallest dimension for scene object
                material="default_thermal"
            )

            # Create rigid body with proper dimensions
            dimensions = np.array(domino_size)
            rigid_body = self.rigid_solver.create_box_body(
                body_id=obj_id,
                name=name,
                position=np.array(pos, dtype=float),
                dimensions=dimensions,
                mass=0.5
            )

            created_objects.append(obj_id)

        return {
            "success": True,
            "object_ids": created_objects,
            "message": f"Created domino run with {count} dominoes"
        }

    def _create_newtons_cradle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create Newton's cradle."""
        count = params.get("count", 5)
        position = params.get("position", [0, 2, 0])

        # Initialize rigid body solver if needed
        if self.rigid_solver is None:
            self.rigid_solver = RigidBodySolver()

        created_objects = []
        sphere_radius = 0.2
        spacing = sphere_radius * 2.0  # Spheres just touching

        for i in range(count):
            pos = [position[0] + (i - count/2) * spacing, position[1], position[2]]
            name = f"cradle_sphere_{i:02d}"

            obj_id = self.scene.create_sphere(
                name=name,
                position=tuple(pos),
                radius=sphere_radius,
                material="default_thermal"
            )

            # Create rigid body
            rigid_body = self.rigid_solver.create_sphere_body(
                body_id=obj_id,
                name=name,
                position=np.array(pos, dtype=float),
                radius=sphere_radius,
                mass=1.0
            )

            created_objects.append(obj_id)

        return {
            "success": True,
            "object_ids": created_objects,
            "message": f"Created Newton's cradle with {count} spheres"
        }

    def _create_fixed_joint(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a fixed joint between two bodies."""
        body1_name = params.get("body1", "")
        body2_name = params.get("body2", "")

        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}

        # Find bodies by name
        body1_id = None
        body2_id = None

        for body in self.rigid_solver.bodies.values():
            if body.name == body1_name:
                body1_id = body.id
            elif body.name == body2_name:
                body2_id = body.id

        if not body1_id or not body2_id:
            return {"success": False, "error": f"Bodies '{body1_name}' or '{body2_name}' not found"}

        try:
            constraint_id = f"fixed_joint_{body1_name}_{body2_name}"
            constraint = self.rigid_solver.create_fixed_joint(constraint_id, body1_id, body2_id)

            return {
                "success": True,
                "constraint_id": constraint_id,
                "message": f"Created fixed joint between '{body1_name}' and '{body2_name}'"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _create_hinge_joint(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a hinge joint between two bodies."""
        body1_name = params.get("body1", "")
        body2_name = params.get("body2", "")
        anchor = params.get("anchor", [0, 0, 0])
        axis = params.get("axis", [0, 0, 1])

        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}

        # Find bodies by name
        body1_id = None
        body2_id = None

        for body in self.rigid_solver.bodies.values():
            if body.name == body1_name:
                body1_id = body.id
            elif body.name == body2_name:
                body2_id = body.id

        if not body1_id or not body2_id:
            return {"success": False, "error": f"Bodies '{body1_name}' or '{body2_name}' not found"}

        try:
            constraint_id = f"hinge_joint_{body1_name}_{body2_name}"
            constraint = self.rigid_solver.create_hinge_joint(
                constraint_id, body1_id, body2_id,
                np.array(anchor), np.array(axis)
            )

            return {
                "success": True,
                "constraint_id": constraint_id,
                "message": f"Created hinge joint between '{body1_name}' and '{body2_name}'"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _create_distance_constraint(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a distance constraint between two bodies."""
        body1_name = params.get("body1", "")
        body2_name = params.get("body2", "")
        distance = params.get("distance", 1.0)

        if self.rigid_solver is None:
            return {"success": False, "error": "No physics simulation active"}

        # Find bodies by name
        body1_id = None
        body2_id = None

        for body in self.rigid_solver.bodies.values():
            if body.name == body1_name:
                body1_id = body.id
            elif body.name == body2_name:
                body2_id = body.id

        if not body1_id or not body2_id:
            return {"success": False, "error": f"Bodies '{body1_name}' or '{body2_name}' not found"}

        try:
            constraint_id = f"distance_{body1_name}_{body2_name}"
            constraint = self.rigid_solver.create_distance_constraint(
                constraint_id, body1_id, body2_id, distance
            )

            return {
                "success": True,
                "constraint_id": constraint_id,
                "message": f"Created distance constraint between '{body1_name}' and '{body2_name}'"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}