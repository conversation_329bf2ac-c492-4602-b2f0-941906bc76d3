#!/usr/bin/env python3
"""
Test enhanced timeline and replay system.
"""

import sys
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.scene import Scene
from core.simulation_recorder import SimulationRecorder
from nlp.parser import parse_command
from runtime.action_executor import ActionExecutor

def test_timeline_recording():
    """Test timeline recording and replay functionality."""
    print("Testing timeline recording and replay...")
    
    scene = Scene("Timeline Test")
    executor = ActionExecutor(scene)
    recorder = SimulationRecorder()
    
    # Create some objects
    print("\n1. Creating physics objects...")
    commands = [
        "create a physics sphere ball1 at position 0,2,0 with mass 1kg",
        "create a physics sphere ball2 at position 1,2,0 with mass 1kg",
        "create a physics box box1 at position 0,4,0 with mass 2kg"
    ]
    
    for cmd in commands:
        actions = parse_command(cmd)
        for action in actions:
            result = executor.execute(action)
            if result["success"]:
                print(f"  ✓ {result['message']}")
            else:
                print(f"  ❌ {result['error']}")
    
    # Start recording
    print("\n2. Starting simulation recording...")
    recorder.start_recording("Test simulation with falling objects")
    
    # Run physics simulation and record frames
    print("\n3. Running physics simulation...")
    if executor.rigid_solver:
        for i in range(30):  # Record 30 frames
            # Step physics
            result = executor.step_physics(1.0/60.0)
            
            if result["success"]:
                stats = result.get("stats", {})
                current_time = stats.get("time", i * (1.0/60.0))
                
                # Record frame
                recorder.record_frame(current_time, executor.rigid_solver, scene)
                
                if i % 10 == 0:
                    print(f"  Frame {i}: Time={current_time:.3f}s, Energy={stats.get('total_energy', 0):.2f}J")
    
    # Stop recording
    recorder.stop_recording()
    print(f"\n4. Recording complete: {recorder.get_frame_count()} frames, {recorder.get_duration():.3f}s")
    
    # Save recording to temporary file
    print("\n5. Testing save/load functionality...")
    with tempfile.NamedTemporaryFile(suffix='.h5', delete=False) as tmp_file:
        tmp_path = tmp_file.name
    
    try:
        recorder.save_to_hdf5(tmp_path)
        print(f"  ✓ Saved recording to {tmp_path}")
        
        # Load recording
        new_recorder = SimulationRecorder()
        success = new_recorder.load_from_hdf5(tmp_path)
        
        if success:
            print(f"  ✓ Loaded recording: {new_recorder.get_frame_count()} frames")
            
            # Test frame retrieval
            print("\n6. Testing frame retrieval...")
            
            # Get frame at different times
            test_times = [0.0, 0.1, 0.25, 0.5]
            for test_time in test_times:
                frame = new_recorder.get_frame_at_time(test_time)
                if frame:
                    print(f"  Frame at {test_time}s: {len(frame.bodies)} bodies, Energy={frame.stats.get('total_energy', 0):.2f}J")
                else:
                    print(f"  No frame found at {test_time}s")
            
            # Test replay functionality
            print("\n7. Testing replay functionality...")
            print("  Simulating timeline scrubbing...")
            
            # Simulate scrubbing through the timeline
            scrub_times = [0.0, 0.1, 0.2, 0.15, 0.05, 0.3]
            for scrub_time in scrub_times:
                frame = new_recorder.get_frame_at_time(scrub_time)
                if frame:
                    print(f"    Scrubbed to {scrub_time}s -> Frame {frame.frame_number} (actual time: {frame.time:.3f}s)")
        else:
            print("  ❌ Failed to load recording")
    
    finally:
        # Clean up temporary file
        Path(tmp_path).unlink(missing_ok=True)
    
    print("\n8. Testing JSON export...")
    with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as tmp_file:
        json_path = tmp_file.name
    
    try:
        recorder.save_to_json(json_path)
        print(f"  ✓ Saved recording to JSON: {json_path}")
        
        # Check file size
        file_size = Path(json_path).stat().st_size
        print(f"  File size: {file_size} bytes")
        
    finally:
        # Clean up temporary file
        Path(json_path).unlink(missing_ok=True)
    
    # Print final scene summary
    print(f"\nFinal scene summary:")
    summary = scene.get_summary()
    print(f"  Objects: {summary['num_objects']}")
    print(f"  Fields: {summary['num_fields']}")
    
    if executor.rigid_solver:
        print(f"  Rigid bodies: {len(executor.rigid_solver.bodies)}")
        final_stats = executor.rigid_solver.get_stats()
        print(f"  Final energy: {final_stats.get('total_energy', 0):.2f}J")

if __name__ == "__main__":
    test_timeline_recording()
    print("\n🎉 Timeline and replay test complete!")
